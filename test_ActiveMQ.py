import json
import os
import stomp
import time


def load_config():
    """Load configuration file"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Configuration file {config_path} not found")
        raise
    except json.JSONDecodeError:
        print(f"Configuration file {config_path} format error")
        raise


class MyListener(stomp.ConnectionListener):

    def on_error(self, headers, message):
        print(f"Error: {message}")

    def on_message(self, headers, message):
        print(f"\nReceived message: {message}")

    def on_disconnected(self):
        print("Disconnected from ActiveMQ")

    def on_connected(self, headers, body):
        print("Successfully connected to ActiveMQ")


def test_activemq_connection():
    # Load configuration
    config = load_config()
    activemq_config = config['activemq']

    # Configure ActiveMQ connection
    conn = stomp.Connection([(activemq_config['host'], activemq_config['port'])
                             ])  # STOMP port
    conn.set_listener('', MyListener())

    try:
        # Connect with configured user credentials
        conn.connect(activemq_config['user'],
                     activemq_config['passwd'],
                     wait=True)
        # Subscribe to queue
        conn.subscribe(destination=activemq_config['queue'], id=1, ack='auto')
        print(
            f"Successfully subscribed to queue {activemq_config['queue']}, starting to listen..."
        )

        # Keep connection alive for configured timeout to receive messages
        time.sleep(activemq_config['connection_timeout'])
    except Exception as e:
        print(f"Connection failed: {e}")
    finally:
        conn.disconnect()


if __name__ == "__main__":
    test_activemq_connection()
