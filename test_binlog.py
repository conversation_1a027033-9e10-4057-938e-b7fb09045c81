import json
import os
import pymysql
from pymysqlreplication import BinLogStreamReader
from pymysqlreplication.row_event import (DeleteRowsEvent, UpdateRowsEvent,
                                          WriteRowsEvent)


def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件 {config_path} 不存在")
        raise
    except json.JSONDecodeError:
        print(f"配置文件 {config_path} 格式错误")
        raise


def listen_for_changes():
    # 加载配置
    config = load_config()

    # 配置binlog监听
    stream = BinLogStreamReader(
        connection_settings=config['database'],
        server_id=config['binlog']['server_id'],
        blocking=config['binlog']['blocking'],
        resume_stream=config['binlog']['resume_stream'],
        only_events=[DeleteRowsEvent, WriteRowsEvent, UpdateRowsEvent])

    print("开始监听数据库变更...")
    try:
        for binlogevent in stream:
            for row in binlogevent.rows:
                print(f"\n事件类型: {type(binlogevent)}")
                print(f"变更数据: {row}")
    except KeyboardInterrupt:
        print("\n停止监听")
    finally:
        stream.close()


if __name__ == "__main__":
    listen_for_changes()
