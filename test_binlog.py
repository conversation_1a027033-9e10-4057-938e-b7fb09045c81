import json
import os
import pymysql
from pymysqlreplication import BinLogStreamReader
from pymysqlreplication.row_event import (DeleteRowsEvent, UpdateRowsEvent,
                                          WriteRowsEvent)


def load_config():
    """Load configuration file"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Configuration file {config_path} not found")
        raise
    except json.JSONDecodeError:
        print(f"Configuration file {config_path} format error")
        raise


def listen_for_changes():
    # Load configuration
    config = load_config()

    # Configure binlog listener
    stream = BinLogStreamReader(
        connection_settings=config['database'],
        server_id=config['binlog']['server_id'],
        blocking=config['binlog']['blocking'],
        resume_stream=config['binlog']['resume_stream'],
        only_events=[DeleteRowsEvent, WriteRowsEvent, UpdateRowsEvent])

    print("Starting to listen for database changes...")
    try:
        for binlogevent in stream:
            for row in binlogevent.rows:
                print(f"\nEvent type: {type(binlogevent)}")
                print(f"Changed data: {row}")
    except KeyboardInterrupt:
        print("\nStopping listener")
    finally:
        stream.close()


if __name__ == "__main__":
    listen_for_changes()
